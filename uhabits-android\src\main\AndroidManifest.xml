<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:name=".HabitsApplication"
        android:allowBackup="true"
        android:backupAgent=".HabitsBackupAgent"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/main_activity_title"
        android:localeConfig="@xml/locales_config"
        android:supportsRtl="true"
        android:theme="@style/AppBaseTheme">

        <activity
            android:name=".activities.habits.edit.EditHabitActivity"
            android:exported="true">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.habits.list.ListHabitsActivity" />
        </activity>

        <meta-data
            android:name="com.google.android.backup.api_key"
            android:value="AEdPqrEAAAAI6aeWncbnMNo8E5GWeZ44dlc5cQ7tCROwFhOtiw" />

        <activity
            android:name=".activities.habits.list.ListHabitsActivity"
            android:exported="true"
            android:label="@string/main_activity_title"
            android:launchMode="singleTop" />

        <activity-alias
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/main_activity_title"
            android:launchMode="singleTop"
            android:targetActivity=".activities.habits.list.ListHabitsActivity">
            <intent-filter android:label="@string/main_activity_title">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name=".activities.habits.show.ShowHabitActivity"
            android:label="@string/title_activity_show_habit">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.habits.list.ListHabitsActivity" />
        </activity>

        <activity
            android:name=".activities.settings.SettingsActivity"
            android:label="@string/settings">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.habits.list.ListHabitsActivity" />
        </activity>

        <activity
            android:name=".activities.intro.IntroActivity"
            android:label=""
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <activity
            android:name=".widgets.activities.HabitPickerDialog"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Light.Dialog">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>
        </activity>

        <activity
            android:name=".widgets.activities.BooleanHabitPickerDialog"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Light.Dialog">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>
        </activity>

        <activity
            android:name=".widgets.activities.NumericalHabitPickerDialog"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.Light.Dialog">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
            </intent-filter>
        </activity>

        <activity
            android:name=".activities.about.AboutActivity"
            android:label="@string/about">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.habits.list.ListHabitsActivity" />
        </activity>

        <activity
            android:name=".notifications.SnoozeDelayPickerActivity"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name=".widgets.CheckmarkWidgetProvider"
            android:exported="true"
            android:label="@string/checkmark">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_checkmark_info" />
        </receiver>

        <service
            android:name=".widgets.StackWidgetService"
            android:exported="false"
            android:permission="android.permission.BIND_REMOTEVIEWS" />

        <receiver
            android:name=".widgets.HistoryWidgetProvider"
            android:exported="true"
            android:label="@string/history">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_history_info" />
        </receiver>

        <receiver
            android:name=".widgets.ScoreWidgetProvider"
            android:exported="true"
            android:label="@string/score">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_score_info" />
        </receiver>

        <receiver
            android:name=".widgets.StreakWidgetProvider"
            android:exported="true"
            android:label="@string/streaks">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_streak_info" />
        </receiver>

        <receiver
            android:name=".widgets.FrequencyWidgetProvider"
            android:exported="true"
            android:label="@string/frequency">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_frequency_info" />
        </receiver>

        <receiver
            android:name=".widgets.TargetWidgetProvider"
            android:exported="true"
            android:label="@string/target">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_target_info" />
        </receiver>

        <receiver
            android:name=".receivers.ReminderReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver android:name=".receivers.WidgetReceiver"
            android:exported="true"
            android:permission="false">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
                <action android:name="org.isoron.uhabits.ACTION_SET_NUMERICAL_VALUE" />
                <data
                    android:host="org.isoron.uhabits"
                    android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="org.isoron.uhabits.ACTION_TOGGLE_REPETITION" />

                <data
                    android:host="org.isoron.uhabits"
                    android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="org.isoron.uhabits.ACTION_ADD_REPETITION" />

                <data
                    android:host="org.isoron.uhabits"
                    android:scheme="content" />
            </intent-filter>
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="org.isoron.uhabits.ACTION_REMOVE_REPETITION" />

                <data
                    android:host="org.isoron.uhabits"
                    android:scheme="content" />
            </intent-filter>
        </receiver>

        <!-- Locale/Tasker -->
        <activity
            android:name=".automation.EditSettingActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.EDIT_SETTING" />
            </intent-filter>
        </activity>

        <!-- Locale/Tasker -->
        <receiver
            android:name=".automation.FireSettingReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.twofortyfouram.locale.intent.action.FIRE_SETTING" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="org.isoron.uhabits"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>
