<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="0dp"
        android:paddingTop="16dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:orientation="vertical">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical">

        <RadioButton
                android:id="@+id/everyDayRadioButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/every_day" />

    </LinearLayout>

    <LinearLayout
            android:id="@+id/everyXDaysContainer"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical">

        <RadioButton
                android:id="@+id/everyXDaysRadioButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        <EditText
                android:id="@+id/everyXDaysTextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:background="@drawable/bg_input_box"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:inputType="number"
                android:maxLength="3"
                android:text="3" />

    </LinearLayout>

    <LinearLayout
            android:id="@+id/xTimesPerWeekContainer"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical">

        <RadioButton
                android:id="@+id/xTimesPerWeekRadioButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        <EditText
                android:id="@+id/xTimesPerWeekTextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:background="@drawable/bg_input_box"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:inputType="number"
                android:maxLength="1"
                android:text="3" />


    </LinearLayout>

    <LinearLayout
            android:id="@+id/xTimesPerMonthContainer"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:gravity="center_vertical">

        <RadioButton
                android:id="@+id/xTimesPerMonthRadioButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        <EditText
                android:id="@+id/xTimesPerMonthTextView"
                android:layout_width="50dp"
                android:layout_height="40dp"
                android:gravity="center"
                android:background="@drawable/bg_input_box"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:inputType="number"
                android:maxLength="2"
                android:text="10" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/xTimesPerYDaysContainer"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical">

        <RadioButton
            android:id="@+id/xTimesPerYDaysRadioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <EditText
            android:id="@+id/xTimesPerYDaysXTextView"
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:background="@drawable/bg_input_box"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:inputType="number"
            android:maxLength="3"
            android:text="3" />


        <EditText
            android:id="@+id/xTimesPerYDaysYTextView"
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:background="@drawable/bg_input_box"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:inputType="number"
            android:maxLength="3"
            android:text="14" />

    </LinearLayout>

</LinearLayout>
