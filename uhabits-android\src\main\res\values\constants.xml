<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->
<resources>
    <string name="helpURL">http://loophabits.org/faq.html</string>
    <string name="playStoreURL">market://details?id=org.isoron.uhabits</string>
    <string name="feedbackURL" formatted="false">mailto:<EMAIL>?subject=Feedback%20about%20Loop%20Habit%20Tracker</string>
    <string name="privacyPolicyURL" formatted="false">http://loophabits.org/privacy</string>
    <string name="codeContributorsURL" formatted="false">https://github.com/iSoron/uhabits/graphs/contributors</string>
    <string name="sourceCodeURL">https://github.com/iSoron/uhabits</string>
    <string name="translateURL">http://translate.loophabits.org/</string>
    <string name="bugReportTo"><EMAIL></string>
    <string name="bugReportSubject">Bug Report - Loop Habit Tracker</string>
    <string name="syncBaseURL" formatted="false">https://sync.loophabits.org</string>

    <string-array name="snooze_picker_names">
        <item>@string/interval_15_minutes</item>
        <item>@string/interval_30_minutes</item>
        <item>@string/interval_1_hour</item>
        <item>@string/interval_2_hour</item>
        <item>@string/interval_4_hour</item>
        <item>@string/interval_8_hour</item>
        <item>@string/interval_24_hour</item>
        <item>@string/interval_custom</item>
    </string-array>

    <integer-array name="snooze_picker_values" translatable="false">
        <item>15</item>
        <item>30</item>
        <item>60</item>
        <item>120</item>
        <item>240</item>
        <item>480</item>
        <item>1440</item>
        <item>-1</item>
    </integer-array>

    <string-array name="actions_yes_no" translatable="false">
        <item>@string/check</item>
        <item>@string/uncheck</item>
        <item>@string/toggle</item>
    </string-array>

    <string-array name="actions_numerical" translatable="false">
        <item>@string/increment</item>
        <item>@string/decrement</item>
    </string-array>

    <string-array name="strengthIntervalNames" translatable="false">
        <item>@string/day</item>
        <item>@string/week</item>
        <item>@string/month</item>
        <item>@string/quarter</item>
        <item>@string/year</item>
    </string-array>

    <string-array name="strengthIntervalNamesWithoutDay" translatable="false">
        <item>@string/week</item>
        <item>@string/month</item>
        <item>@string/quarter</item>
        <item>@string/year</item>
    </string-array>

    <string-array name="widget_opacity_entries">
        <item>100%</item>
        <item>80%</item>
        <item>60%</item>
        <item>40%</item>
        <item>20%</item>
        <item>0%</item>
    </string-array>

    <string-array name="widget_opacity_values">
        <item>255</item>
        <item>204</item>
        <item>153</item>
        <item>102</item>
        <item>51</item>
        <item>0</item>
    </string-array>
</resources>
