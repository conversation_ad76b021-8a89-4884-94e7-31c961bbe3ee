<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (C) 2016-2021 <PERSON><PERSON><PERSON> <***************>
  ~
  ~ This file is part of Loop Habit Tracker.
  ~
  ~ Loop Habit Tracker is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by the
  ~ Free Software Foundation, either version 3 of the License, or (at your
  ~ option) any later version.
  ~
  ~ Loop Habit Tracker is distributed in the hope that it will be useful, but
  ~ WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
  ~ or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  ~ more details.
  ~
  ~ You should have received a copy of the GNU General Public License along
  ~ with this program. If not, see <http://www.gnu.org/licenses/>.
  -->


<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/checkmark_dialog_bg"
    android:minWidth="208dp"
    android:minHeight="128dp"
    android:orientation="vertical"
    app:divider="@drawable/checkmark_dialog_divider"
    app:showDividers="middle">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/notes"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/transparent"
        android:gravity="center"
        android:hint="@string/notes"
        android:inputType="textCapSentences|textMultiLine"
        android:padding="4dp"
        android:text=""
        android:textSize="@dimen/smallTextSize" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/booleanButtons"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:divider="@drawable/checkmark_dialog_divider"
        app:showDividers="middle">

        <TextView
            android:id="@+id/yesBtn"
            style="@style/CheckmarkPopupBtn"
            android:text="@string/fa_check" />

        <TextView
            android:id="@+id/skipBtn"
            style="@style/CheckmarkPopupBtn"
            android:text="@string/fa_skipped" />

        <TextView
            android:id="@+id/noBtn"
            style="@style/CheckmarkPopupBtn"
            android:text="@string/fa_times" />

        <TextView
            android:id="@+id/unknownBtn"
            style="@style/CheckmarkPopupBtn"
            android:text="@string/fa_question" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/numberButtons"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:divider="@drawable/checkmark_dialog_divider"
        app:showDividers="middle">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/value"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@color/transparent"
            android:inputType="numberDecimal"
            android:selectAllOnFocus="true"
            android:textAlignment="center"
            android:textSize="@dimen/smallTextSize" />

        <TextView
            android:id="@+id/saveBtn"
            style="@style/NumericalPopupBtn"
            android:text="@string/save" />

        <TextView
            android:id="@+id/skipBtnNumber"
            style="@style/NumericalPopupBtn"
            android:text="@string/skip_day" />

        <TextView
            android:id="@+id/unknownBtnNumber"
            style="@style/CheckmarkPopupBtn"
            android:text="@string/fa_question" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.appcompat.widget.LinearLayoutCompat>