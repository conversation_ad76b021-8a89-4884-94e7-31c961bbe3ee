<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.4" type="baseline" client="gradle" dependencies="false" name="AGP (8.1.4)" variant="all" version="8.1.4">

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            notificationManager.notify(notificationId, notification)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/notifications/AndroidNotificationTray.kt"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        errorLine1="            notificationManager.notify(notificationId, n)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/notifications/AndroidNotificationTray.kt"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Missing permissions required by intent Intent.ACTION_CLOSE_SYSTEM_DIALOGS: android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS"
        errorLine1="        context.sendBroadcast(Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS))"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/receivers/ReminderController.kt"
            line="79"
            column="17"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="                val lines = DateUtils.formatHeaderDate(day).toUpperCase().split(&quot;\n&quot;)"
        errorLine2="                                                            ~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HeaderView.kt"
            line="144"
            column="61"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                    String.format(&quot;%02d&quot;, hours_24[i]) : String.format(&quot;%d&quot;, hours[i]);"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/RadialPickerLayout.java"
            line="198"
            column="21"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                    String.format(&quot;%02d&quot;, hours_24[i]) : String.format(&quot;%d&quot;, hours[i]);"
        errorLine2="                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/RadialPickerLayout.java"
            line="198"
            column="58"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            innerHoursTexts[i] = String.format(&quot;%d&quot;, hours[i]);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/RadialPickerLayout.java"
            line="199"
            column="34"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            minutesTexts[i] = String.format(&quot;%02d&quot;, minutes[i]);"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/RadialPickerLayout.java"
            line="200"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        canvas.drawText(String.format(&quot;%d&quot;, day), x, y, mMonthNumPaint);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/SimpleMonthView.java"
            line="41"
            column="25"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            String announcement = String.format(&quot;%d&quot;, newValue);"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/TimePickerDialog.java"
            line="441"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="                        deletedKeyStr = String.format(&quot;%d&quot;, getValFromKeyCode(deleted));"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/TimePickerDialog.java"
            line="574"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="        Utils.tryAccessibilityAnnounce(mTimePicker, String.format(&quot;%d&quot;, val));"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/TimePickerDialog.java"
            line="641"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        errorLine1="            years.add(String.format(&quot;%d&quot;, year));"
        errorLine2="                      ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/YearPickerView.java"
            line="75"
            column="23"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `localeConfig` is only used in API level 33 and higher (current min is 28)"
        errorLine1="        android:localeConfig=&quot;@xml/locales_config&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="UseRequireInsteadOfGet"
        message="Use requireContext() instead of context!!"
        errorLine1="        val sr = StyledResources(context!!)"
        errorLine2="                                 ~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/settings/SettingsFragment.kt"
            line="92"
            column="34"/>
    </issue>

    <issue
        id="FragmentTagUsage"
        message="Replace the &lt;fragment> tag with FragmentContainerView."
        errorLine1="    &lt;fragment"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/settings_activity.xml"
            line="34"
            column="6"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        val entireView = inflater.inflate(R.layout.widget_wrapper, null)"
        errorLine2="                                                                   ~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/widgets/BaseWidget.kt"
            line="129"
            column="68"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View view = inflater.inflate(R.layout.date_picker_dialog, null);"
        errorLine2="                                                                  ~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/DatePickerDialog.java"
            line="201"
            column="67"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        View view = inflater.inflate(R.layout.time_picker_dialog, null);"
        errorLine2="                                                                  ~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/time/TimePickerDialog.java"
            line="219"
            column="67"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="    return inflater.inflate(R.layout.toolbar, null) as Toolbar"
        errorLine2="                                              ~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/utils/ViewExtensions.kt"
            line="99"
            column="47"/>
    </issue>

    <issue
        id="MissingInflatedId"
        message="`@layout/widget_configure_activity` does not contain a declaration with id `buttonSave`"
        errorLine1="        val saveButton = findViewById&lt;Button>(R.id.buttonSave)"
        errorLine2="                                              ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/widgets/activities/HabitPickerDialog.kt"
            line="84"
            column="47"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/main_activity_title&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="AppCompatCustomView"
        message="This custom view should extend `androidx.appcompat.widget.AppCompatTextView` instead"
        errorLine1="public class AccessibleTextView extends TextView {"
        errorLine2="                                        ~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/AccessibleTextView.java"
            line="30"
            column="41"/>
    </issue>

    <issue
        id="AppCompatCustomView"
        message="This custom view should extend `androidx.appcompat.widget.AppCompatTextView` instead"
        errorLine1="public class TextViewWithCircularIndicator extends TextView {"
        errorLine2="                                                   ~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/TextViewWithCircularIndicator.java"
            line="33"
            column="52"/>
    </issue>

    <issue
        id="ExtraText"
        message="Unexpected text found in layout file: &quot;>&quot;"
        errorLine1=">"
        errorLine2="~">
        <location
            file="src/main/res/layout/about.xml"
            line="147"
            column="1"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes (`@dimen/date_picker_header_text_size` is defined as `14dp` in `values/pickers.xml`"
        errorLine1="    android:textSize=&quot;@dimen/date_picker_header_text_size&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_header_view.xml"
            line="25"
            column="5"/>
        <location
            file="src/main/res/values/pickers.xml"
            line="81"
            column="48"
            message="This dp dimension is used as a text size"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes (`@dimen/selected_date_month_size` is defined as `30dp` in `values/pickers.xml`"
        errorLine1="            android:textSize=&quot;@dimen/selected_date_month_size&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_selected_date.xml"
            line="44"
            column="13"/>
        <location
            file="src/main/res/values/pickers.xml"
            line="80"
            column="44"
            message="This dp dimension is used as a text size"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes (`@dimen/selected_date_day_size` is defined as `75dp` in `values/pickers.xml`"
        errorLine1="            android:textSize=&quot;@dimen/selected_date_day_size&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_selected_date.xml"
            line="57"
            column="13"/>
        <location
            file="src/main/res/values/pickers.xml"
            line="79"
            column="42"
            message="This dp dimension is used as a text size"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes (`@dimen/selected_date_year_size` is defined as `30dp` in `values/pickers.xml`"
        errorLine1="        android:textSize=&quot;@dimen/selected_date_year_size&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_selected_date.xml"
            line="68"
            column="9"/>
        <location
            file="src/main/res/values/pickers.xml"
            line="78"
            column="43"
            message="This dp dimension is used as a text size"/>
    </issue>

    <issue
        id="SpUsage"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes (`@dimen/year_label_text_size` is defined as `22dp` in `values/pickers.xml`"
        errorLine1="    android:textSize=&quot;@dimen/year_label_text_size&quot; />"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/year_label_text_view.xml"
            line="24"
            column="5"/>
        <location
            file="src/main/res/values/pickers.xml"
            line="85"
            column="40"
            message="This dp dimension is used as a text size"/>
    </issue>

    <issue
        id="UseCompatLoadingForDrawables"
        message="Use `ResourcesCompat.getDrawable()`"
        errorLine1="                {getContext().getResources().getDrawable(R.drawable.color_picker_swatch)};"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/colorpicker/ColorPickerSwatch.java"
            line="64"
            column="18"/>
    </issue>

    <issue
        id="StringFormatInvalid"
        message="Format string &apos;`x_times_per_month`&apos; is not a valid format string so it should not be passed to `String.format`"
        errorLine1="    freqDen == 30 || freqDen == 31 -> resources.getString(R.string.x_times_per_month, freqNum)"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/edit/EditHabitActivity.kt"
            line="63"
            column="39"/>
        <location
            file="src/main/res/values-ko-rKR/strings.xml"
            line="184"
            column="5"
            message="This definition does not require arguments"/>
    </issue>

    <issue
        id="StringFormatInvalid"
        message="Format string &apos;`x_times_per_week`&apos; is not a valid format string so it should not be passed to `String.format`"
        errorLine1="    freqDen == 7 -> resources.getString(R.string.x_times_per_week, freqNum)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/edit/EditHabitActivity.kt"
            line="67"
            column="21"/>
        <location
            file="src/main/res/values-ko-rKR/strings.xml"
            line="183"
            column="5"
            message="This definition does not require arguments"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;ca&quot; (Catalan) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-ca-rES/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;ca&quot; (Catalan) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-ca-rES/strings.xml"
            line="35"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_deleted&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="35"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;toast_habits_deleted&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="35"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_deleted&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="35"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_deleted&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="35"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_archived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="39"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;toast_habits_archived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="39"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_archived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="39"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_archived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="39"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;ca&quot; (Catalan) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-ca-rES/strings.xml"
            line="80"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;ca&quot; (Catalan) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-ca-rES/strings.xml"
            line="84"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="89"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="89"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="89"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="89"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;es&quot; (Spanish) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="93"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;fr&quot; (French) the following quantity should also be defined: `many` (e.g. &quot;1000000 de jours&quot;)"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="93"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;it&quot; (Italian) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="93"
            column="5"/>
    </issue>

    <issue
        id="MissingQuantity"
        message="For locale &quot;pt&quot; (Portuguese) the following quantity should also be defined: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="93"
            column="5"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;helpURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;helpURL&quot;>http://loophabits.org/faq.html&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;playStoreURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;playStoreURL&quot;>market://details?id=org.isoron.uhabits&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;feedbackURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;feedbackURL&quot; formatted=&quot;false&quot;>mailto:<EMAIL>?subject=Feedback%20about%20Loop%20Habit%20Tracker&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;privacyPolicyURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;privacyPolicyURL&quot; formatted=&quot;false&quot;>http://loophabits.org/privacy&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;codeContributorsURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;codeContributorsURL&quot; formatted=&quot;false&quot;>https://github.com/iSoron/uhabits/graphs/contributors&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;sourceCodeURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;sourceCodeURL&quot;>https://github.com/iSoron/uhabits&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;translateURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;translateURL&quot;>http://translate.loophabits.org/&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;bugReportTo&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;bugReportTo&quot;><EMAIL>&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;bugReportSubject&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;bugReportSubject&quot;>Bug Report - Loop Habit Tracker&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="MissingTranslation"
        message="&quot;syncBaseURL&quot; is not translated in &quot;de&quot; (German), &quot;hi&quot; (Hindi), &quot;no&quot; (Norwegian), &quot;fi&quot; (Finnish), &quot;ru&quot; (Russian), &quot;pt&quot; (Portuguese), &quot;bg&quot; (Bulgarian), &quot;hr&quot; (Croatian), &quot;fr&quot; (French), &quot;hu&quot; (Hungarian), &quot;hy&quot; (Armenian), &quot;ug&quot; (Uyghur), &quot;uk&quot; (Ukrainian), &quot;sk&quot; (Slovak), &quot;sl&quot; (Slovenian), &quot;ca&quot; (Catalan), &quot;sr&quot; (Serbian), &quot;sv&quot; (Swedish), &quot;ko&quot; (Korean), &quot;af&quot; (Afrikaans), &quot;in&quot; (Indonesian), &quot;el&quot; (Greek), &quot;eo&quot; (Esperanto), &quot;it&quot; (Italian), &quot;ta&quot; (Tamil), &quot;es&quot; (Spanish), &quot;iw&quot; (Hebrew), &quot;zh&quot; (Chinese), &quot;cs&quot; (Czech), &quot;eu&quot; (Basque), &quot;ar&quot; (Arabic), &quot;te&quot; (Telugu), &quot;vi&quot; (Vietnamese), &quot;ja&quot; (Japanese), &quot;fa&quot; (Persian), &quot;pl&quot; (Polish), &quot;da&quot; (Danish), &quot;ro&quot; (Romanian), &quot;nl&quot; (Dutch), &quot;tr&quot; (Turkish)"
        errorLine1="    &lt;string name=&quot;syncBaseURL&quot; formatted=&quot;false&quot;>https://sync.loophabits.org&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/constants.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="Typos"
        message="&quot;Bem vindo&quot; is a common misspelling; did you mean &quot;Bem-vindo&quot;?"
        errorLine1="    &lt;string name=&quot;intro_title_1&quot;>Bem vindo&lt;/string>"
        errorLine2="                                 ^">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="62"
            column="34"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>عادت تغییر داده شد&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Habitude modifiée&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>आदत हटाई गई&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-hi-rIN/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Hábito modificado.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Привычка изменена&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Navika izmenjena&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Навика измењена&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Звичку змінено&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>عادت حذف شد.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Habitude supprimée&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>आदत संग्रहित की गई&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-hi-rIN/strings.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Hábito deletado.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Navika obrisana&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="37"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Навика обрисана&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="37"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Привычка удалена&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Звичку вилучено&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>عادت بایگانی شد.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Habitude archivée&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>आदत असंग्रहीत की गई&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-hi-rIN/strings.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Hábito arquivado.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="40"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Navika arhivirana&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Навика архивирана&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>عادت از بایگانی خارج شد.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Habitude désarchivée&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Hábito desarquivado.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Привычка архивирована&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Звичку архівовано&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="44"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Navika dearhivirana&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="47"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Навика деархивирана&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="47"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Привычка возвращена из архива&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Звичку розархівовано&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>حذف عادت؟&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="90"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Supprimer cette habitude ?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="90"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Excluir hábito?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="90"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>आदत हटाये?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-hi-rIN/strings.xml"
            line="92"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Obrisati navike?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="93"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Обрисати навикe?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="93"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>این عادت برای همیشه حذف خواهد شد و قابل بازیابی نخواهد بود.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fa-rIR/strings.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>L\&apos;habitude va être supprimée définitivement. Cette action ne peut pas être annulée.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>O hábito será excluído permanentemente. Esta ação não pode ser desfeita.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (0, 1), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>आदत हमेशा के लिये हटा दी जाएगी। इसे वापस नहीं लाया जा सकता।&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-hi-rIN/strings.xml"
            line="96"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Вилучити звичку?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="97"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Удалить привычку?&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="98"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Navike će biti trajno obrisane. Ova radnja je nepovratna.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="98"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Навике ће бити трајно обрисане. Ова радња је неповратна.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="98"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Звичка вилучиться назавжди. Цю дію неможливо скасувати.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="103"
            column="9"/>
    </issue>

    <issue
        id="ImpliedQuantity"
        message="The quantity `&apos;one&apos;` matches more than one specific number in this locale (1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …), but the message did not \&#xA;include a formatting argument (such as `%d`). This is usually an internationalization error. See full issue \&#xA;explanation for more."
        errorLine1="        &lt;item quantity=&quot;one&quot;>Привычка будет удалена. Это действие невозможно отменить.&lt;/item>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="104"
            column="9"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;selected&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;color_swatch_description_selected&quot; translatable=&quot;false&quot;>Color &lt;xliff:g example=&quot;14&quot; id=&quot;color_index&quot;>%1$d&lt;/xliff:g> selected&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="88"
            column="5"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;every_x_days&quot;>Every %d days&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="157"
            column="5"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;weeks&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;every_x_weeks&quot;>Every %d weeks&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="158"
            column="5"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;times&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;x_times_per_week&quot;>%d times per week&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="209"
            column="5"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;times&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;x_times_per_month&quot;>%d times per month&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="210"
            column="5"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;times&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;x_times_per_y_days&quot;>%d times in %d days&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="211"
            column="5"/>
    </issue>

    <issue
        id="StringFormatCount"
        message="Inconsistent number of arguments in formatting string `x_times_per_week`; found both 1 here and 0 in values-ko-rKR/strings.xml"
        errorLine1="    &lt;string name=&quot;x_times_per_week&quot;>%d times per week&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="209"
            column="5"/>
        <location
            file="src/main/res/values-ko-rKR/strings.xml"
            line="183"
            column="5"
            message="Conflicting number of arguments (0) here"/>
    </issue>

    <issue
        id="StringFormatCount"
        message="Inconsistent number of arguments in formatting string `x_times_per_month`; found both 1 here and 0 in values-ko-rKR/strings.xml"
        errorLine1="    &lt;string name=&quot;x_times_per_month&quot;>%d times per month&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="210"
            column="5"/>
        <location
            file="src/main/res/values-ko-rKR/strings.xml"
            line="184"
            column="5"
            message="Conflicting number of arguments (0) here"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_changed&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="31"
            column="5"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_deleted&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="37"
            column="5"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_archived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;toast_habits_unarchived&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="49"
            column="5"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_title&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="97"
            column="5"/>
    </issue>

    <issue
        id="UnusedQuantity"
        message="For language &quot;iw&quot; (Hebrew) the following quantities are not relevant: `many`"
        errorLine1="    &lt;plurals name=&quot;delete_habits_message&quot;>"
        errorLine2="    ^">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="103"
            column="5"/>
    </issue>

    <issue
        id="ExportedReceiver"
        message="Exported receiver does not require permission"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="272"
            column="10"/>
    </issue>

    <issue
        id="KnownPermissionError"
        message="`false` is not a valid permission value"
        errorLine1="            android:permission=&quot;false&quot;>"
        errorLine2="                                ~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="223"
            column="33"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardListAdapter.kt"
            line="74"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardListAdapter.kt"
            line="256"
            column="9"/>
    </issue>

    <issue
        id="Recycle"
        message="This `InputStream` should be freed up after use with `#close()`"
        errorLine1="            val inStream = activity.contentResolver.openInputStream(data.data!!)!!"
        errorLine2="                                                    ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/ListHabitsScreen.kt"
            line="136"
            column="53"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v21`) is unnecessary; `minSdkVersion` is 28. Merge all the resources in this folder into `drawable`.">
        <location
            file="src/main/res/drawable-v21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="This folder configuration (`v26`) is unnecessary; `minSdkVersion` is 28. Merge all the resources in this folder into `mipmap-anydpi`.">
        <location
            file="src/main/res/mipmap-anydpi-v26"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="This `AsyncTask` class should be static or leaks might occur (org.isoron.uhabits.tasks.AndroidTaskRunner.CustomAsyncTask)"
        errorLine1="    private inner class CustomAsyncTask(val task: Task) : AsyncTask&lt;Void?, Int?, Void?>() {"
        errorLine2="                        ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/tasks/AndroidTaskRunner.kt"
            line="57"
            column="25"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (1667 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="            android:pathData=&quot;M212,0.7c-40.2,6.2 -67.7,15.9 -100.7,35.6 -19.2,11.4 -46.1,35 -57.3,50.2 -2.4,3.3 -5.3,6.9 -6.3,8 -6.1,6.4 -19.7,29.6 -27,46 -8.7,19.6 -16,44.9 -19.2,66.6 -2.1,14.6 -2.1,55.2 0,69.8 2.9,20 10.6,47.1 18.1,64.1 4.3,9.8 13.4,27.2 15.4,29.5 1,1.1 2.5,3.4 3.3,5 2.9,5.7 15,22 21.6,29 11.1,11.7 24.4,24.4 28.6,27.2 2.2,1.5 6.7,4.8 10,7.4 24.6,19 63.2,35.1 102.5,42.6 13.9,2.6 68.1,2.6 82,-0 39.3,-7.5 77.9,-23.6 102.5,-42.6 3.3,-2.6 7.8,-5.9 10,-7.4 4.2,-2.8 17.5,-15.5 28.6,-27.2 6.6,-7 18.7,-23.3 21.6,-29 0.8,-1.6 2.3,-3.9 3.3,-5 4.5,-5.2 16.5,-30.7 22.2,-47.5 9.8,-28.6 12.8,-47.9 12.8,-81 0,-26.6 -1.1,-37.3 -6,-57.5 -5,-20.8 -13.7,-44.3 -22.3,-59.5 -8.2,-14.8 -15.8,-26.7 -19.4,-30.5 -1,-1.1 -3.9,-4.7 -6.3,-8 -11.2,-15.2 -38.1,-38.8 -57.3,-50.2 -25.5,-15.2 -45.9,-23.7 -73.2,-30.4 -20.9,-5.1 -29.4,-5.9 -58.5,-5.8 -14.6,0.1 -27.6,0.3 -29,0.6zM209,101.5c6,3.1 6,3.2 6,50.7 0,41.1 -0.1,43.4 -1.9,46.2 -2.5,3.7 -5.7,5.5 -9.8,5.6 -3.8,-0 -7,-2.6 -22.1,-18l-10.4,-10.5 -2.4,3c-23.6,28.6 -28.4,67.7 -12.3,99.7 12.2,24.2 34.2,42 59.9,48.4 10.5,2.7 33.5,2.6 44,-0 32,-8 58.6,-34.6 66.6,-66.6 2.9,-11.4 2.6,-34.6 -0.4,-45.9 -4.9,-17.9 -14.7,-33 -30.5,-46.9 -6.4,-5.6 -5.7,-7.3 9.1,-21.8 14.7,-14.5 16.3,-15.8 19.2,-14.9 2.8,0.9 17.6,15.7 22.9,22.9 13.1,17.7 21.6,36.1 25.9,56 2.2,10.3 2.6,14.6 2.6,28.6 0,23.7 -3.4,38.8 -13.4,59.5 -7.2,15.1 -14.2,24.8 -27,37.5 -12.7,12.8 -22.4,19.8 -37.5,27 -20.5,9.9 -35.8,13.3 -59,13.4 -18.2,-0 -27.7,-1.5 -44.5,-7 -40.2,-13.3 -73.8,-47.1 -87,-87.4 -14.1,-43 -6.2,-89.6 21.2,-126 3.1,-4.2 6.6,-8.4 7.8,-9.5l2.1,-1.9 -12.7,-12.7c-12.3,-12.3 -12.7,-12.8 -13.2,-17.7 -0.5,-5.8 0.7,-8.6 5.1,-11.2 3,-1.9 5.4,-2 46,-2 36.3,-0 43.2,0.2 45.7,1.5z&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_notification.xml"
            line="28"
            column="31"/>
    </issue>

    <issue
        id="MergeRootFrame"
        message="This `&lt;FrameLayout>` can be replaced with a `&lt;merge>` tag"
        errorLine1="&lt;FrameLayout"
        errorLine2="^">
        <location
            file="src/main/res/layout/widget_empty_activity.xml"
            line="21"
            column="1"/>
    </issue>

    <issue
        id="DisableBaselineAlignment"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance"
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_overview.xml"
            line="29"
            column="6"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?windowBackgroundColor` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="            android:background=&quot;?windowBackgroundColor&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?attr/contrast0` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;?attr/contrast0&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="26"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?attr/windowBackgroundColor` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="              android:background=&quot;?attr/windowBackgroundColor&quot;>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/automation.xml"
            line="26"
            column="15"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#ffff00` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;#ffff00&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/canvas_test.xml"
            line="24"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/date_picker_view_animator` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;@color/date_picker_view_animator&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_dialog.xml"
            line="20"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/calendar_header` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;@color/calendar_header&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_header_view.xml"
            line="21"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_selected_date.xml"
            line="23"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/date_picker_view_animator` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="     android:background=&quot;@color/date_picker_view_animator&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/date_picker_view_animator.xml"
            line="22"
            column="6"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#a0000000` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="    android:background=&quot;#a0000000&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/select_habit_type.xml"
            line="27"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/AppBaseTheme`)"
        errorLine1="      android:background=&quot;@color/white&quot; >"
        errorLine2="      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="22"
            column="7"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.bg_select_button` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/bg_select_button.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.canvas_test` appears to be unused"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/canvas_test.xml"
            line="21"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.date_picker_header_view` appears to be unused"
        errorLine1="&lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/date_picker_header_view.xml"
            line="17"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.dialog_bg_input_box` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/dialog_bg_input_box.xml"
            line="21"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.done_background_color` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/done_background_color.xml"
            line="16"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.done_background_color_dark` appears to be unused"
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/done_background_color_dark.xml"
            line="16"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.fa_exclamation_circle` appears to be unused"
        errorLine1=" &lt;string translatable=&quot;false&quot; name=&quot;fa_exclamation_circle&quot;>&amp;#xf06a;&lt;/string>"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/fontawesome.xml"
            line="30"
            column="31"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_50&quot;>#FFEBEE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_100&quot;>#FFCDD2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_300&quot;>#E57373&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_400&quot;>#EF5350&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_500&quot;>#F44336&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_600&quot;>#E53935&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_900&quot;>#B71C1C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_A100&quot;>#FF8A80&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_A200&quot;>#FF5252&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="33"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_A400&quot;>#FF1744&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_A700&quot;>#D50000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_50&quot;>#EDE7F6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="37"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_100&quot;>#D1C4E9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_300&quot;>#9575CD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="40"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_400&quot;>#7E57C2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_500&quot;>#673AB7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_800&quot;>#4527A0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="45"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_900&quot;>#311B92&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_A100&quot;>#B388FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="47"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_A200&quot;>#7C4DFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_A400&quot;>#651FFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="49"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_purple_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_purple_A700&quot;>#6200EA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="50"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_50&quot;>#E1F5FE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_100&quot;>#B3E5FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_300&quot;>#4FC3F7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="55"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_400&quot;>#29B6F6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="56"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_500&quot;>#03A9F4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="57"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_800&quot;>#0277BD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="60"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_900&quot;>#01579B&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="61"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_A100&quot;>#80D8FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="62"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_A200&quot;>#40C4FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="63"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_A400&quot;>#00B0FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="64"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_blue_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_blue_A700&quot;>#0091EA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="65"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_50&quot;>#E8F5E9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="67"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_100&quot;>#C8E6C9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="68"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_200&quot;>#A5D6A7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="69"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_300&quot;>#81C784&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="70"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_400&quot;>#66BB6A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="71"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_500&quot;>#4CAF50&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="72"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_600&quot;>#43A047&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="73"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_800&quot;>#2E7D32&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="75"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_900&quot;>#1B5E20&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="76"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_A100&quot;>#B9F6CA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="77"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_A400&quot;>#00E676&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="79"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.green_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;green_A700&quot;>#00C853&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="80"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_50&quot;>#FFFDE7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="82"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_100&quot;>#FFF9C4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="83"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_300&quot;>#FFF176&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="85"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_400&quot;>#FFEE58&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="86"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_500&quot;>#FFEB3B&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="87"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_600&quot;>#FDD835&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="88"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_700&quot;>#FBC02D&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="89"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_900&quot;>#F57F17&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="91"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_A100&quot;>#FFFF8D&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="92"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_A200&quot;>#FFFF00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="93"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_A400&quot;>#FFEA00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="94"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.yellow_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;yellow_A700&quot;>#FFD600&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="95"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_50&quot;>#FBE9E7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="97"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_100&quot;>#FFCCBC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="98"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_300&quot;>#FF8A65&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="100"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_400&quot;>#FF7043&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="101"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_500&quot;>#FF5722&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="102"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_600&quot;>#F4511E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="103"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_900&quot;>#BF360C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="106"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_A100&quot;>#FF9E80&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="107"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_A200&quot;>#FF6E40&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="108"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_A400&quot;>#FF3D00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="109"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.deep_orange_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;deep_orange_A700&quot;>#DD2C00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="110"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_50&quot;>#ECEFF1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="112"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_100&quot;>#CFD8DC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="113"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_200&quot;>#B0BEC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="114"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_300&quot;>#90A4AE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="115"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_400&quot;>#78909C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="116"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_500&quot;>#607D8B&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="117"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_600&quot;>#546E7A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="118"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_700&quot;>#455A64&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="119"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_800&quot;>#37474F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="120"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_grey_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_grey_900&quot;>#263238&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="121"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_50&quot;>#FCE4EC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="123"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_100&quot;>#F8BBD0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="124"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_300&quot;>#F06292&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="126"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_400&quot;>#EC407A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="127"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_500&quot;>#E91E63&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="128"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_800&quot;>#AD1457&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="131"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_900&quot;>#880E4F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="132"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_A100&quot;>#FF80AB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="133"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_A200&quot;>#FF4081&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="134"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_A400&quot;>#F50057&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="135"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.pink_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;pink_A700&quot;>#C51162&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="136"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_50&quot;>#E8EAF6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="138"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_100&quot;>#C5CAE9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="139"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_300&quot;>#7986CB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="141"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_400&quot;>#5C6BC0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="142"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_600&quot;>#3949AB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="144"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_900&quot;>#1A237E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="147"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_A100&quot;>#8C9EFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="148"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_A200&quot;>#536DFE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="149"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_A400&quot;>#3D5AFE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="150"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.indigo_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;indigo_A700&quot;>#304FFE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="151"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_50&quot;>#E0F7FA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="153"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_100&quot;>#B2EBF2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="154"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_300&quot;>#4DD0E1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="156"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_400&quot;>#26C6DA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="157"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_500&quot;>#00BCD4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="158"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_800&quot;>#00838F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="161"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_900&quot;>#006064&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="162"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_A100&quot;>#84FFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="163"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_A200&quot;>#18FFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="164"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_A400&quot;>#00E5FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="165"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.cyan_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;cyan_A700&quot;>#00B8D4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="166"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_50&quot;>#F1F8E9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="168"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_100&quot;>#DCEDC8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="169"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_300&quot;>#AED581&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="171"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_400&quot;>#9CCC65&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="172"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_500&quot;>#8BC34A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="173"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_700&quot;>#689F38&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="175"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_800&quot;>#558B2F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="176"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_900&quot;>#33691E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="177"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_A100&quot;>#CCFF90&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="178"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_A200&quot;>#B2FF59&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="179"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_A400&quot;>#76FF03&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="180"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_green_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_green_A700&quot;>#64DD17&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="181"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_50&quot;>#FFF8E1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="183"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_200&quot;>#FFE082&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="185"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_300&quot;>#FFD54F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="186"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_400&quot;>#FFCA28&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="187"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_500&quot;>#FFC107&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="188"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_600&quot;>#FFB300&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="189"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_700&quot;>#FFA000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="190"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_900&quot;>#FF6F00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="192"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_A100&quot;>#FFE57F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="193"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_A200&quot;>#FFD740&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="194"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_A400&quot;>#FFC400&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="195"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.amber_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;amber_A700&quot;>#FFAB00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="196"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_50&quot;>#EFEBE9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="198"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_100&quot;>#D7CCC8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="199"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_300&quot;>#A1887F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="201"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_400&quot;>#8D6E63&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="202"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_500&quot;>#795548&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="203"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_600&quot;>#6D4C41&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="204"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.brown_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;brown_900&quot;>#3E2723&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="207"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_50&quot;>#F3E5F5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="209"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_100&quot;>#E1BEE7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="210"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_300&quot;>#BA68C8&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="212"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_400&quot;>#AB47BC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="213"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#9C27B0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="214"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_800&quot;>#6A1B9A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="217"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_900&quot;>#4A148C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="218"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_A100&quot;>#EA80FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="219"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_A200&quot;>#E040FB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="220"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_A400&quot;>#D500F9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="221"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_A700&quot;>#AA00FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="222"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_50&quot;>#E3F2FD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="224"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_100&quot;>#BBDEFB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="225"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_200&quot;>#90CAF9&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="226"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_400&quot;>#42A5F5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="228"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_500&quot;>#2196F3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="229"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_600&quot;>#1E88E5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="230"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_900&quot;>#0D47A1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="233"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_A100&quot;>#82B1FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="234"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_A200&quot;>#448AFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="235"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_A400&quot;>#2979FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="236"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_A700&quot;>#2962FF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="237"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_50&quot;>#E0F2F1&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="239"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_100&quot;>#B2DFDB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="240"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_300&quot;>#4DB6AC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="242"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_400&quot;>#26A69A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="243"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_500&quot;>#009688&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="244"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_800` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_800&quot;>#00695C&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="247"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_900&quot;>#004D40&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="248"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_A100&quot;>#A7FFEB&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="249"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_A200&quot;>#64FFDA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="250"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_A400&quot;>#1DE9B6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="251"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_A700&quot;>#00BFA5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="252"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_50&quot;>#F9FBE7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="254"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_100&quot;>#F0F4C3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="255"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_300&quot;>#DCE775&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="257"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_400&quot;>#D4E157&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="258"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_500&quot;>#CDDC39&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="259"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_600&quot;>#C0CA33&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="260"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_900&quot;>#827717&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="263"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_A100&quot;>#F4FF81&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="264"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_A200&quot;>#EEFF41&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="265"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_A400&quot;>#C6FF00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="266"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.lime_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;lime_A700&quot;>#AEEA00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="267"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_50` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_50&quot;>#FFF3E0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="269"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_100` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_100&quot;>#FFE0B2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="270"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_300` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_300&quot;>#FFB74D&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="272"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_400` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_400&quot;>#FFA726&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="273"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_500&quot;>#FF9800&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="274"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_600` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_600&quot;>#FB8C00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="275"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_900` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_900&quot;>#E65100&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="278"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_A100` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_A100&quot;>#FFD180&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="279"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_A200` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_A200&quot;>#FFAB40&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="280"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_A400` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_A400&quot;>#FF9100&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="281"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.orange_A700` appears to be unused"
        errorLine1="    &lt;color name=&quot;orange_A700&quot;>#FF6D00&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="282"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.grey_875` appears to be unused"
        errorLine1="    &lt;color name=&quot;grey_875&quot;>#282828&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="296"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_ae` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_ae&quot;>#ef000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="303"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_ac` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_ac&quot;>#cf000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="304"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_a8` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_a8&quot;>#8f000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="306"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_a6` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_a6&quot;>#6f000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="307"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_a4` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_a4&quot;>#4f000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="308"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_a2` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_a2&quot;>#2f000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="309"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black_a0` appears to be unused"
        errorLine1="    &lt;color name=&quot;black_a0&quot;>#0f000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="310"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_ae` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_ae&quot;>#efffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="312"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_ac` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_ac&quot;>#cfffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="313"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_a8` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_a8&quot;>#8fffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="315"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_a6` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_a6&quot;>#6fffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="316"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_a4` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_a4&quot;>#4fffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="317"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white_a2` appears to be unused"
        errorLine1="    &lt;color name=&quot;white_a2&quot;>#2fffffff&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/material_colors.xml"
            line="318"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.number_picker_dialog` appears to be unused"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/number_picker_dialog.xml"
            line="21"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.footer_height` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;footer_height&quot;>48dip&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="65"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.minimum_margin_sides` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;minimum_margin_sides&quot;>48dip&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.minimum_margin_top_bottom` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;minimum_margin_top_bottom&quot;>24dip&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="67"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.date_picker_header_height` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;date_picker_header_height&quot;>30dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="70"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.done_button_height` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;done_button_height&quot;>42dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="73"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.date_picker_header_text_size` appears to be unused"
        errorLine1="    &lt;dimen name=&quot;date_picker_header_text_size&quot;>14dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="81"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.done_text_color_normal` appears to be unused"
        errorLine1="    &lt;color name=&quot;done_text_color_normal&quot;>#000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="109"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.done_text_color_disabled` appears to be unused"
        errorLine1="    &lt;color name=&quot;done_text_color_disabled&quot;>#cccccc&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="110"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.blue_focused` appears to be unused"
        errorLine1="    &lt;color name=&quot;blue_focused&quot;>#c1e8f7&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="115"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.neutral_pressed` appears to be unused"
        errorLine1="    &lt;color name=&quot;neutral_pressed&quot;>#33999999&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="116"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.calendar_header` appears to be unused"
        errorLine1="    &lt;color name=&quot;calendar_header&quot;>#999999&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="119"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.calendar_selected_date_text` appears to be unused"
        errorLine1="    &lt;color name=&quot;calendar_selected_date_text&quot;>#ffd1d2d4&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="121"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.done_text_color_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;done_text_color_dark&quot;>#888888&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="123"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red` appears to be unused"
        errorLine1="    &lt;color name=&quot;red&quot;>#ff3333&lt;/color>"
        errorLine2="           ~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="128"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.red_focused` appears to be unused"
        errorLine1="    &lt;color name=&quot;red_focused&quot;>#853333&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="129"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.light_gray` appears to be unused"
        errorLine1="    &lt;color name=&quot;light_gray&quot;>#404040&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="130"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.dark_gray` appears to be unused"
        errorLine1="    &lt;color name=&quot;dark_gray&quot;>#363636&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="131"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.line_dark` appears to be unused"
        errorLine1="    &lt;color name=&quot;line_dark&quot;>#808080&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/pickers.xml"
            line="132"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.preference_category_custom` appears to be unused"
        errorLine1="&lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/preference_category_custom.xml"
            line="18"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.habit_strength` appears to be unused"
        errorLine1="    &lt;string name=&quot;habit_strength&quot;>Habit strength&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.long_press_to_toggle` appears to be unused"
        errorLine1="    &lt;string name=&quot;long_press_to_toggle&quot;>Press-and-hold to check or uncheck&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.interval_always_ask` appears to be unused"
        errorLine1="    &lt;string name=&quot;interval_always_ask&quot;>Always ask&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.every_x_weeks` appears to be unused"
        errorLine1="    &lt;string name=&quot;every_x_weeks&quot;>Every %d weeks&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="158"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.led_notifications` appears to be unused"
        errorLine1="    &lt;string name=&quot;led_notifications&quot;>Notification light&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="168"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.led_notifications_description` appears to be unused"
        errorLine1="    &lt;string name=&quot;led_notifications_description&quot;>Shows a blinking light for reminders. Only available in phones with LED notification lights.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="169"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.long_press_to_edit` appears to be unused"
        errorLine1="    &lt;string name=&quot;long_press_to_edit&quot;>Press-and-hold to change the value&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="183"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.value` appears to be unused"
        errorLine1="    &lt;string name=&quot;value&quot;>Value&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="184"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Preference_Category_Material` appears to be unused"
        errorLine1="    &lt;style name=&quot;Preference.Category.Material&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="251"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.DialogWithTitle` appears to be unused"
        errorLine1="    &lt;style name=&quot;DialogWithTitle&quot; parent=&quot;@style/Theme.AppCompat.Light.Dialog&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="255"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.DarkDialogWithTitle` appears to be unused"
        errorLine1="    &lt;style name=&quot;DarkDialogWithTitle&quot;"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="259"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.DialogFormInnerBox` appears to be unused"
        errorLine1="    &lt;style name=&quot;DialogFormInnerBox&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="370"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.DialogFormLabel` appears to be unused"
        errorLine1="    &lt;style name=&quot;DialogFormLabel&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="379"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_Transparent` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.Transparent&quot; parent=&quot;android:Theme&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="417"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.dialogFormText` appears to be unused"
        errorLine1="    &lt;style name=&quot;dialogFormText&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_dialog.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.dialogFormLabel` appears to be unused"
        errorLine1="    &lt;style name=&quot;dialogFormLabel&quot; parent=&quot;dialogFormText&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_dialog.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.dialogFormRow` appears to be unused"
        errorLine1="    &lt;style name=&quot;dialogFormRow&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles_dialog.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/checkmark_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/checkmark_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/history_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/history_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/score_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/score_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/streak_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/streak_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/target_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="RedundantNamespace"
        message="This namespace declaration is redundant"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/target_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="TooManyViews"
        message="`about_translators.xml` has more than 80 views, bad for performance"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Adam Jurkiewicz&quot; />"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="81"
            column="5"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/checkmark_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/checkmark_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/history_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/history_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/score_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/score_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/streak_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/streak_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;StackView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/target_stackview_widget.xml"
            line="24"
            column="16"/>
    </issue>

    <issue
        id="UnusedNamespace"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        errorLine1="    &lt;TextView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/target_stackview_widget.xml"
            line="29"
            column="15"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Egendefinert...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-no-rNO/strings.xml"
            line="58"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personalizar...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rPT/strings.xml"
            line="59"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Diferit...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="src/main/res/values-ro-rRO/strings.xml"
            line="59"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>مخصص...   &lt;/string>"
        errorLine2="                                   ~~~~~~~">
        <location
            file="src/main/res/values-ar-rSA/strings.xml"
            line="60"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Vlastní...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~">
        <location
            file="src/main/res/values-cs-rCZ/strings.xml"
            line="60"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Vlastné ...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~">
        <location
            file="src/main/res/values-sk-rSK/strings.xml"
            line="66"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personalitza...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ca-rES/strings.xml"
            line="68"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Pertsonalizatua...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-eu-rES/strings.xml"
            line="72"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Kustom...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~">
        <location
            file="src/main/res/values-in-rID/strings.xml"
            line="72"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>カスタム...&lt;/string>"
        errorLine2="                                   ~~~~~~~">
        <location
            file="src/main/res/values-ja-rJP/strings.xml"
            line="72"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Aangepast...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~">
        <location
            file="src/main/res/values-nl-rNL/strings.xml"
            line="72"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Tùy chỉnh...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~">
        <location
            file="src/main/res/values-vi-rVN/strings.xml"
            line="72"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Персонализирано...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-bg-rBG/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Brugerdefineret...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-da-rDK/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Benutzerdefiniert...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-de-rDE/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personalizar...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-es-rES/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personnaliser...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-fr-rFR/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Egyedi...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~">
        <location
            file="src/main/res/values-hu-rHU/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personalizza...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-it-rIT/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Personalizar...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pt-rBR/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Anpassad...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~">
        <location
            file="src/main/res/values-sv-rSE/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Özel...&lt;/string>"
        errorLine2="                                   ~~~~~~~">
        <location
            file="src/main/res/values-tr-rTR/strings.xml"
            line="76"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Prilagođeno...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rCS/strings.xml"
            line="80"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Прилагођено...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-sr-rSP/strings.xml"
            line="80"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Custom...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="81"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>התאמה אישית...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-iw-rIL/strings.xml"
            line="84"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Własne ustawienia...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-pl-rPL/strings.xml"
            line="84"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Настроить...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~">
        <location
            file="src/main/res/values-ru-rRU/strings.xml"
            line="84"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;interval_custom&quot;>Налаштувати...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-uk-rUA/strings.xml"
            line="84"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;view_all_contributors&quot;>Преглед на всички сътрудници...&lt;/string>"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-bg-rBG/strings.xml"
            line="184"
            column="42"/>
    </issue>

    <issue
        id="IconDipSize"
        message="The image `ic_launcher_monochrome.png` varies significantly in its density-independent (dip) size across the various density versions: mipmap-hdpi/ic_launcher_monochrome.png: 108x108 dp (162x162 px), mipmap-mdpi/ic_launcher_monochrome.png: 162x162 dp (162x162 px), mipmap-xhdpi/ic_launcher_monochrome.png: 108x108 dp (216x216 px), mipmap-xxhdpi/ic_launcher_monochrome.png: 108x108 dp (324x324 px), mipmap-xxxhdpi/ic_launcher_monochrome.png: 108x108 dp (432x432 px)">
        <location
            file="src/main/res/mipmap-xxxhdpi/ic_launcher_monochrome.png"/>
        <location
            file="src/main/res/mipmap-xhdpi/ic_launcher_monochrome.png"/>
        <location
            file="src/main/res/mipmap-xxhdpi/ic_launcher_monochrome.png"/>
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher_monochrome.png"/>
        <location
            file="src/main/res/mipmap-mdpi/ic_launcher_monochrome.png"/>
    </issue>

    <issue
        id="IconDuplicatesConfig"
        message="The `ic_launcher_monochrome.png` icon has identical contents in the following configuration folders: mipmap-hdpi, mipmap-mdpi">
        <location
            file="src/main/res/mipmap-mdpi/ic_launcher_monochrome.png"/>
        <location
            file="src/main/res/mipmap-hdpi/ic_launcher_monochrome.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/intro_icon_1.png` in densityless folder">
        <location
            file="src/main/res/drawable/intro_icon_1.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/intro_icon_2.png` in densityless folder">
        <location
            file="src/main/res/drawable/intro_icon_2.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/intro_icon_4.png` in densityless folder">
        <location
            file="src/main/res/drawable/intro_icon_4.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/stripe.png` in densityless folder">
        <location
            file="src/main/res/drawable/stripe.png"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="162"
            column="22"/>
    </issue>

    <issue
        id="AlwaysShowAction"
        message="Prefer &quot;`ifRoom`&quot; instead of &quot;`always`&quot;"
        errorLine1="        app:showAsAction=&quot;always&quot;/>"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/list_habits.xml"
            line="30"
            column="9"/>
        <location
            file="src/main/res/menu/list_habits.xml"
            line="37"
            column="9"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                        &lt;EditText"
        errorLine2="                         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="91"
            column="26"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="130"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="162"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                        &lt;EditText"
        errorLine2="                         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="185"
            column="26"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_edit_habit.xml"
            line="258"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="58"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="83"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="109"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="134"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="147"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/number_picker_dialog.xml"
            line="103"
            column="18"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `CheckmarkButtonView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class CheckmarkButtonView("
        errorLine2="      ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/CheckmarkButtonView.kt"
            line="55"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `CheckmarkPanelView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class CheckmarkPanelView("
        errorLine2="      ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/CheckmarkPanelView.kt"
            line="39"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `ColorPickerSwatch` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="public class ColorPickerSwatch extends FrameLayout implements View.OnClickListener {"
        errorLine2="             ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/colorpicker/ColorPickerSwatch.java"
            line="31"
            column="14"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `GraphWidgetView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class GraphWidgetView(context: Context?, val dataView: View) : HabitWidgetView(context) {"
        errorLine2="      ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/widgets/views/GraphWidgetView.kt"
            line="27"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `HabitCardListView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class HabitCardListView("
        errorLine2="      ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardListView.kt"
            line="52"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `HabitCardView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class HabitCardView("
        errorLine2="      ~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardView.kt"
            line="61"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `HeaderView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class HeaderView("
        errorLine2="      ~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HeaderView.kt"
            line="42"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `HintView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class HintView("
        errorLine2="      ~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HintView.kt"
            line="34"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `ListHabitsRootView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class ListHabitsRootView @Inject constructor("
        errorLine2="      ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/ListHabitsRootView.kt"
            line="60"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `NumberButtonView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class NumberButtonView("
        errorLine2="      ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/NumberButtonView.kt"
            line="69"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `NumberPanelView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class NumberPanelView("
        errorLine2="      ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/NumberPanelView.kt"
            line="39"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `TaskProgressBar` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="class TaskProgressBar("
        errorLine2="      ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/uhabits/activities/common/views/TaskProgressBar.kt"
            line="28"
            column="7"/>
    </issue>

    <issue
        id="ViewConstructor"
        message="Custom view `YearPickerView` is missing constructor used by tools: `(Context)` or `(Context,AttributeSet)` or `(Context,AttributeSet,int)`"
        errorLine1="public class YearPickerView extends ListView implements OnItemClickListener, OnDateChangedListener {"
        errorLine2="             ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/YearPickerView.java"
            line="41"
            column="14"/>
    </issue>

    <issue
        id="GetContentDescriptionOverride"
        message="Overriding `getContentDescription()` on a View is not recommended"
        errorLine1="    public CharSequence getContentDescription() {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/android/datetimepicker/date/TextViewWithCircularIndicator.java"
            line="80"
            column="25"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view `AndroidDataView` overrides `onTouchEvent` but not `performClick`"
        errorLine1="    override fun onTouchEvent(event: MotionEvent) = detector.onTouchEvent(event)"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/java/org/isoron/platform/gui/AndroidDataView.kt"
            line="47"
            column="18"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view ``HabitCardView`` has `setOnTouchListener` called on it but does not override `performClick`"
        errorLine1="        cardView.setOnTouchListener { _, ev ->"
        errorLine2="        ^">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardListView.kt"
            line="105"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        errorLine1="        cardView.setOnTouchListener { _, ev ->"
        errorLine2="                                    ^">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardListView.kt"
            line="105"
            column="37"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        errorLine1="            setOnTouchListener { v, event ->"
        errorLine2="                               ^">
        <location
            file="src/main/java/org/isoron/uhabits/activities/habits/list/views/HabitCardView.kt"
            line="195"
            column="32"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                &lt;ImageView"
        errorLine2="                 ~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="46"
            column="18"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/color_picker_swatch.xml"
            line="17"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/color_picker_swatch.xml"
            line="21"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/widget_wrapper.xml"
            line="28"
            column="6"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="58"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="83"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="109"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="134"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="147"
            column="10"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Álinson S. Xavier (@iSoron)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Álinson S. Xavier (@iSoron)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="123"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Quentin Hibon (@hiqua)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Quentin Hibon (@hiqua)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="124"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Oleg Ivashchenko (@olegivo)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Oleg Ivashchenko (@olegivo)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="125"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Kristian Tashkov (@KristianTashkov)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Kristian Tashkov (@KristianTashkov)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="126"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Jakub Kalinowski (@kalina559)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Jakub Kalinowski (@kalina559)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="127"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Rechee Jozil (@recheej)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Rechee Jozil (@recheej)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="128"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sebastian Gallese (@sgallese)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sebastian Gallese (@sgallese)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="129"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Luboš Luňák (@llunak)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Luboš Luňák (@llunak)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="130"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bindu (@vbh)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Bindu (@vbh)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="131"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Victor Yu (@vyu1)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Victor Yu (@vyu1)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="132"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Christoph Hennemann (@chennemann)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Christoph Hennemann (@chennemann)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="133"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Денис (@sciamano)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Денис (@sciamano)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="134"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Joseph Tran (@JotraN)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Joseph Tran (@JotraN)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="135"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Nikhil (@regularcoder)&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Nikhil (@regularcoder)&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="136"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;JanetQC&quot;, should use `@string` resource"
        errorLine1="                &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;JanetQC&quot;/>"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about.xml"
            line="137"
            column="53"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bahasa Indonesia&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Bahasa Indonesia&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="4"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Angga Rifandi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Angga Rifandi&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="5"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Dika Fitrian Dwi Putra&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Dika Fitrian Dwi Putra&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="6"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Heru Yen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Heru Yen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="7"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Intan Ayunda&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Intan Ayunda&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="8"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Neysa Nasywa&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Neysa Nasywa&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="9"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;azzamsa&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;azzamsa&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="10"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;raden20&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;raden20&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="11"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Català&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Català&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="12"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;David Nos&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;David Nos&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="13"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;carllacan&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;carllacan&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="14"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Cрпски&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Cрпски&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="15"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Rancher&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Rancher&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="16"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Dansk&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Dansk&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="17"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Aputsiak Niels Janussen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Aputsiak Niels Janussen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="18"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sølv Ræven&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sølv Ræven&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="19"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Yussuf&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Yussuf&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="20"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;fbruna17&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;fbruna17&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="21"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Deutsch&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Deutsch&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="22"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Can Altas&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Can Altas&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="23"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Laura Sophie&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Laura Sophie&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="24"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Marius Teufelweich&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Marius Teufelweich&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="25"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Matthias Meisser&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Matthias Meisser&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="26"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Michael&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Michael&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="27"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;cobalt59&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;cobalt59&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="28"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;fabian.bouchal&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;fabian.bouchal&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="29"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;sojusnik&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;sojusnik&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="30"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;tat bz&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;tat bz&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="31"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Español&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Español&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="32"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ander Raso Vazquez&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ander Raso Vazquez&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="33"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Eilif Adelvice&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Eilif Adelvice&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="34"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Iabin Arteaga&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Iabin Arteaga&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="35"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sebastian05067&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sebastian05067&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="36"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;luiandresgonzalez&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;luiandresgonzalez&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="37"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;oscfd&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;oscfd&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="38"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Esperanto&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Esperanto&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="39"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;4001982248998&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;4001982248998&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="40"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;marco.baturan&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;marco.baturan&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="41"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Euskara&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Euskara&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="42"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Beriain&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Beriain&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="43"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Osoitz&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Osoitz&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="44"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;beriain&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;beriain&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="45"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Français&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Français&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="46"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;François Mahé&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;François Mahé&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="47"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mathis Chenuet&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mathis Chenuet&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="48"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Michael Faille&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Michael Faille&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="49"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Pierre GALIEGUE&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Pierre GALIEGUE&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="50"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Samuel Guay&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Samuel Guay&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="51"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Thibaut Girka&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Thibaut Girka&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="52"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Tiralka&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Tiralka&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="53"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;_translator&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;_translator&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="54"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;roptat&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;roptat&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="55"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Fārsi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Fārsi&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="56"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Behnood HRazy&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Behnood HRazy&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="57"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Eman&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Eman&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="58"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Saeed Esmaili&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Saeed Esmaili&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="59"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Hindi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Hindi&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="60"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ravi Rami&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ravi Rami&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="61"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Vijaykumar Borkar&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Vijaykumar Borkar&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="62"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Hrvatski&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Hrvatski&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="63"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ivan Krušlin&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ivan Krušlin&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="64"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Italiano&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Italiano&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="65"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Marco Cavazza&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Marco Cavazza&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="66"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;androide74&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;androide74&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="67"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Magyar&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Magyar&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="68"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Balázs Keresztury&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Balázs Keresztury&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="69"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Isti&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Isti&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="70"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;gapszi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;gapszi&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="71"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Nederlands&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Nederlands&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="72"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Blinkin&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Blinkin&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="73"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bryanx&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Bryanx&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="74"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Jelle den Butter&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Jelle den Butter&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="75"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mark Macaré&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mark Macaré&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="76"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;chrrris1987&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;chrrris1987&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="77"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Norsk&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Norsk&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="78"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;nitovf9292&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;nitovf9292&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="79"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Polski&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Polski&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="80"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Adam Jurkiewicz&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Adam Jurkiewicz&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="81"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Arkadiusz Bubak&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Arkadiusz Bubak&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="82"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Jan Wojtecki&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Jan Wojtecki&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="83"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;plitwin&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;plitwin&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="84"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Português&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Português&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="85"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Alinson Xavier&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Alinson Xavier&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="86"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bernardo Lopes&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Bernardo Lopes&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="87"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Gustavo Lima&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Gustavo Lima&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="88"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Martim Parente&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Martim Parente&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="89"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sofia Neves&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sofia Neves&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="90"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Thamara Andrade&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Thamara Andrade&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="91"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Română&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Română&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="92"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Andreea Muscalagiu&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Andreea Muscalagiu&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="93"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Andrei Pleș&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Andrei Pleș&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="94"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;StoP4Me&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;StoP4Me&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="95"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Slovak&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Slovak&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="96"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;dukelc&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;dukelc&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="97"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Slovenščina&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Slovenščina&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="98"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Dušan Strgar&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Dušan Strgar&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="99"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Suomen kieli&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Suomen kieli&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="100"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;2kaafone&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;2kaafone&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="101"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Antti Kallio&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Antti Kallio&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="102"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Elina Salminen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Elina Salminen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="103"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sofia Veijonen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sofia Veijonen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="104"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Svenska&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Svenska&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="105"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Alexander Jansson&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Alexander Jansson&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="106"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;David&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;David&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="107"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Robin&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Robin&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="108"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Telugu&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Telugu&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="109"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;easyrepro&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;easyrepro&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="110"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Tiếng Việt&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Tiếng Việt&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="111"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Anh Quân&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Anh Quân&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="112"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Huy Ngo&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Huy Ngo&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="113"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Lương Vĩnh Khang&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Lương Vĩnh Khang&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="114"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Trần Thái&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Trần Thái&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="115"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;bruhwut&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;bruhwut&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="116"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;pnhpnh&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;pnhpnh&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="117"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Türkçe&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Türkçe&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="118"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Alparslan Şakçi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Alparslan Şakçi&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="119"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Caner Başaran&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Caner Başaran&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="120"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Evren&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Evren&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="121"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ishmaeel&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ishmaeel&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="122"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;hodanli&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;hodanli&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="123"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Čeština&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Čeština&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="124"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Tomáš Borovec&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Tomáš Borovec&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="125"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;andaryon&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;andaryon&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="126"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;boban77&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;boban77&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="127"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ελληνικά&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Ελληνικά&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="128"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Andreas Michelakis&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Andreas Michelakis&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="129"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;DionysosDV&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;DionysosDV&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="130"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Български&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Български&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="131"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mihail Stefanov&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mihail Stefanov&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="132"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Русский&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Русский&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="133"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Andrew Firnes&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Andrew Firnes&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="134"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Diana Karaseva&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Diana Karaseva&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="135"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Dmitriy Bogdanov&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Dmitriy Bogdanov&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="136"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Tanya&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Tanya&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="137"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;engineeringforgood&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;engineeringforgood&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="138"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Українська&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;Українська&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="139"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Andrij Mizyk&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Andrij Mizyk&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="140"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Oglaigh Rystard&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Oglaigh Rystard&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="141"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Prosta4ok_ua&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Prosta4ok_ua&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="142"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Rystard&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Rystard&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="143"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Yurii Stavytskyi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Yurii Stavytskyi&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="144"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;taras-ko&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;taras-ko&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="145"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;српски&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;српски&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="146"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;OP Smosher&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;OP Smosher&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="147"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Slobodan Simić&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Slobodan Simić&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="148"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Đorđe Vasiljević&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Đorđe Vasiljević&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="149"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;עברית‎&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;עברית‎&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="150"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ohad Edri&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ohad Edri&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="151"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Omer I.S.&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Omer I.S.&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="152"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Omry Cohen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Omry Cohen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="153"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Yoav Argov&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Yoav Argov&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="154"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;العَرَبِية‎&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;العَرَبِية‎&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="155"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Al Alloush&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Al Alloush&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="156"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Boula&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Boula&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="157"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Israa Z&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Israa Z&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="158"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mahdi Nasiri&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mahdi Nasiri&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="159"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Michael Malak&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Michael Malak&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="160"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Saeed Esmaili&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Saeed Esmaili&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="161"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sief Tarek&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Sief Tarek&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="162"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;alalloush&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;alalloush&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="163"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;mohmans&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;mohmans&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="164"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;reyhoon&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;reyhoon&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="165"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;فارسی‎&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;فارسی‎&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="166"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mahdi Nasiri&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mahdi Nasiri&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="167"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;हिन्दी&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;हिन्दी&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="168"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Aman Satnami&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Aman Satnami&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="169"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Niraj Yadav&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Niraj Yadav&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="170"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;தமிழ்‎&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;தமிழ்‎&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="171"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Anshoe&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Anshoe&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="172"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Magimai Prakasam&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Magimai Prakasam&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="173"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Mohammed Imthath&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Mohammed Imthath&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="174"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;magimai&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;magimai&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="175"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;中文&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;中文&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="176"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Bowie Chen&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Bowie Chen&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="177"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;JY3&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;JY3&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="178"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Jo Chuang&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Jo Chuang&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="179"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;JoeLi&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;JoeLi&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="180"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;KMakoto&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;KMakoto&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="181"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Lee&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Lee&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="182"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Limin Lu&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Limin Lu&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="183"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Liveeasy&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Liveeasy&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="184"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Ting-Hua&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Ting-Hua&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="185"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;XuToTo&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;XuToTo&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="186"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;hypnotichemionus&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;hypnotichemionus&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="187"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;yoding&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;yoding&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="188"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;黄克&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;黄克&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="189"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;日本語&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;日本語&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="190"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Naofumi F&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Naofumi F&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="191"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Tomairuka&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Tomairuka&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="192"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;ayane.m&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;ayane.m&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="193"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;長谷川知里&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;長谷川知里&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="194"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;한국어&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item.Language&quot; android:text=&quot;한국어&quot; />"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="195"
            column="50"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Josh Graham&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Josh Graham&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="196"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Seoyul&quot;, should use `@string` resource"
        errorLine1="    &lt;TextView style=&quot;@style/About.Item&quot; android:text=&quot;Seoyul&quot; />"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/about_translators.xml"
            line="197"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;3&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;3&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="68"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;3&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;3&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="93"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;10&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;10&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;3&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;3&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="144"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;14&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;14&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/frequency_picker_dialog.xml"
            line="157"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Randomize&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Randomize&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/show_habit.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;8.5k&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;8.5k&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_subtitle.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;8:00 AM&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;8:00 AM&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_subtitle.xml"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Error drawing widget&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Error drawing widget&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/widget_error.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="RtlSymmetry"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        errorLine1="          android:paddingLeft=&quot;?android:attr/listPreferredItemPaddingLeft&quot;"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/preference_category_custom.xml"
            line="24"
            column="11"/>
    </issue>

    <issue
        id="RtlSymmetry"
        message="When you define `paddingStart` you should probably also define `paddingEnd` for right-to-left symmetry"
        errorLine1="          android:paddingStart=&quot;?android:attr/listPreferredItemPaddingLeft&quot;"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/preference_category_custom.xml"
            line="25"
            column="11"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `paddingLeft`; already defining `paddingStart` with `targetSdkVersion` 34"
        errorLine1="          android:paddingLeft=&quot;?android:attr/listPreferredItemPaddingLeft&quot;"
        errorLine2="          ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/preference_category_custom.xml"
            line="24"
            column="11"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34"
        errorLine1="            android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_bar.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34"
        errorLine1="            android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_bar.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34"
        errorLine1="            android:layout_alignParentLeft=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_bar.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34"
        errorLine1="            android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_score.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Redundant attribute `layout_alignParentLeft`; already defining `layout_alignParentStart` with `targetSdkVersion` 34"
        errorLine1="            android:layout_alignParentLeft=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/show_habit_score.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toLeftOf` with `android:layout_toStartOf=&quot;@+id/separator&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_toLeftOf=&quot;@+id/separator&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignRight` with `android:layout_alignEnd=&quot;@+id/hour_space&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignRight=&quot;@+id/hour_space&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignLeft` with `android:layout_alignStart=&quot;@+id/hour_space&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignLeft=&quot;@+id/hour_space&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignRight` with `android:layout_alignEnd=&quot;@+id/center_view&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignRight=&quot;@+id/center_view&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/separator&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_toRightOf=&quot;@+id/separator&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignRight` with `android:layout_alignEnd=&quot;@+id/minutes_space&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignRight=&quot;@+id/minutes_space&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignLeft` with `android:layout_alignStart=&quot;@+id/minutes_space&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignLeft=&quot;@+id/minutes_space&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignLeft` with `android:layout_alignStart=&quot;@+id/ampm_label&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignLeft=&quot;@+id/ampm_label&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="106"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_alignRight` with `android:layout_alignEnd=&quot;@+id/ampm_label&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_alignRight=&quot;@+id/ampm_label&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@+id/minutes_space&quot;` to better support right-to-left layouts"
        errorLine1="            android:layout_toRightOf=&quot;@+id/minutes_space&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/time_header_label.xml"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="IgnoreWithoutReason"
        message="Test is ignored without giving any explanation"
        errorLine1="    @Ignore"
        errorLine2="    ~~~~~~~">
        <location
            file="src/androidTest/java/org/isoron/uhabits/performance/PerformanceTest.kt"
            line="43"
            column="5"/>
    </issue>

    <issue
        id="IgnoreWithoutReason"
        message="Test is ignored without giving any explanation"
        errorLine1="    @Ignore"
        errorLine2="    ~~~~~~~">
        <location
            file="src/androidTest/java/org/isoron/uhabits/performance/PerformanceTest.kt"
            line="56"
            column="5"/>
    </issue>

</issues>
